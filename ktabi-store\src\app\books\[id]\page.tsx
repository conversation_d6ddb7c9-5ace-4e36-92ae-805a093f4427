'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { StarIcon, HeartIcon, ShareIcon, TruckIcon, ShieldCheckIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';

export default function BookDetailPage({ params }: { params: { id: string } }) {
  const [quantity, setQuantity] = useState(1);
  const [selectedFormat, setSelectedFormat] = useState('physical');
  const [isWishlisted, setIsWishlisted] = useState(false);
  const [activeTab, setActiveTab] = useState('description');

  // Sample book data - in real app, fetch based on params.id
  const book = {
    id: 1,
    title: 'مئة عام من العزلة',
    author: 'غا<PERSON>رييل غارسيا ماركيز',
    price: 120,
    originalPrice: 150,
    rating: 4.8,
    reviews: 245,
    category: 'روايات',
    isNew: false,
    isBestseller: true,
    description: 'رواية خيالية للكاتب الكولومبي غابرييل غارسيا ماركيز، نُشرت لأول مرة عام 1967. تحكي الرواية قصة عائلة بوينديا عبر سبعة أجيال في بلدة ماكوندو الخيالية. تُعتبر من أهم أعمال الأدب اللاتيني الأمريكي وحصل ماركيز بسببها على جائزة نوبل للأدب.',
    pages: 448,
    language: 'عربي',
    publisher: 'دار الآداب',
    publishYear: 2020,
    isbn: '978-977-00-1234-5',
    weight: '450 جرام',
    dimensions: '14 × 21 سم',
    inStock: true,
    stockCount: 15,
    formats: [
      { id: 'physical', name: 'نسخة ورقية', price: 120, available: true },
      { id: 'pdf', name: 'نسخة PDF', price: 60, available: true },
      { id: 'epub', name: 'نسخة EPUB', price: 65, available: true },
    ],
    features: [
      'شحن مجاني للطلبات أكثر من 200 جنيه',
      'إمكانية الإرجاع خلال 14 يوم',
      'ضمان الجودة',
      'دفع آمن',
    ],
    tableOfContents: [
      'الفصل الأول: البداية',
      'الفصل الثاني: الأجيال',
      'الفصل الثالث: الحب والحرب',
      'الفصل الرابع: الذكريات',
      'الفصل الخامس: النهاية',
    ],
  };

  const reviews = [
    {
      id: 1,
      name: 'أحمد محمد',
      rating: 5,
      date: '2024-01-15',
      comment: 'رواية رائعة ومترجمة بشكل ممتاز. أنصح بقراءتها لكل محبي الأدب.',
    },
    {
      id: 2,
      name: 'فاطمة علي',
      rating: 4,
      date: '2024-01-10',
      comment: 'قصة معقدة ولكنها جميلة. تحتاج لتركيز أثناء القراءة.',
    },
    {
      id: 3,
      name: 'محمود حسن',
      rating: 5,
      date: '2024-01-05',
      comment: 'من أفضل الروايات التي قرأتها. أسلوب ماركيز مميز جداً.',
    },
  ];

  const relatedBooks = [
    {
      id: 2,
      title: 'الحب في زمن الكوليرا',
      author: 'غابرييل غارسيا ماركيز',
      price: 110,
      rating: 4.7,
    },
    {
      id: 3,
      title: 'خريف البطريرك',
      author: 'غابرييل غارسيا ماركيز',
      price: 95,
      rating: 4.5,
    },
    {
      id: 4,
      title: 'الأسود يليق بك',
      author: 'أحلام مستغانمي',
      price: 95,
      rating: 4.6,
    },
  ];

  const currentFormat = book.formats.find(f => f.id === selectedFormat);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="mb-8 text-sm arabic">
          <ol className="flex items-center space-x-2 space-x-reverse">
            <li><a href="/" className="text-gray-500 hover:text-primary">الرئيسية</a></li>
            <li className="text-gray-400">/</li>
            <li><a href="/books" className="text-gray-500 hover:text-primary">الكتب</a></li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900">{book.category}</li>
            <li className="text-gray-400">/</li>
            <li className="text-gray-900">{book.title}</li>
          </ol>
        </nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-12">
          {/* Book Image */}
          <div className="space-y-4">
            <div className="relative">
              <div className="bg-gray-200 rounded-lg aspect-[3/4] flex items-center justify-center">
                <span className="text-8xl">📖</span>
              </div>
              {book.isNew && (
                <Badge className="absolute top-4 right-4 arabic">جديد</Badge>
              )}
              {book.isBestseller && (
                <Badge variant="secondary" className="absolute top-4 left-4 arabic">
                  الأكثر مبيعاً
                </Badge>
              )}
            </div>
          </div>

          {/* Book Details */}
          <div className="space-y-6">
            <div>
              <Badge variant="outline" className="mb-2 arabic">
                {book.category}
              </Badge>
              <h1 className="text-3xl font-bold text-gray-900 mb-2 arabic">
                {book.title}
              </h1>
              <p className="text-xl text-gray-600 mb-4 arabic">
                بقلم: {book.author}
              </p>
              
              {/* Rating */}
              <div className="flex items-center mb-6">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <StarIcon
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(book.rating)
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300'
                      }`}
                    />
                  ))}
                </div>
                <span className="mr-2 text-lg font-medium">{book.rating}</span>
                <span className="text-gray-500 arabic">({book.reviews} تقييم)</span>
              </div>
            </div>

            {/* Format Selection */}
            <div>
              <h3 className="text-lg font-semibold mb-3 arabic">اختر النسخة:</h3>
              <div className="grid grid-cols-1 gap-3">
                {book.formats.map((format) => (
                  <label
                    key={format.id}
                    className={`flex items-center justify-between p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedFormat === format.id
                        ? 'border-primary bg-primary/5'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center">
                      <input
                        type="radio"
                        name="format"
                        value={format.id}
                        checked={selectedFormat === format.id}
                        onChange={(e) => setSelectedFormat(e.target.value)}
                        className="mr-3"
                      />
                      <span className="arabic">{format.name}</span>
                    </div>
                    <span className="font-semibold text-primary">
                      {format.price} ج.م
                    </span>
                  </label>
                ))}
              </div>
            </div>

            {/* Price */}
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <span className="text-3xl font-bold text-primary">
                    {currentFormat?.price} ج.م
                  </span>
                  {book.originalPrice && selectedFormat === 'physical' && (
                    <span className="text-lg text-gray-500 line-through">
                      {book.originalPrice} ج.م
                    </span>
                  )}
                </div>
                {book.originalPrice && selectedFormat === 'physical' && (
                  <Badge variant="destructive" className="arabic">
                    وفر {book.originalPrice - book.price} ج.م
                  </Badge>
                )}
              </div>

              {/* Quantity */}
              {selectedFormat === 'physical' && (
                <div className="flex items-center mb-4">
                  <span className="text-sm font-medium mr-4 arabic">الكمية:</span>
                  <div className="flex items-center border rounded-md">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-1 hover:bg-gray-100"
                    >
                      -
                    </button>
                    <span className="px-4 py-1 border-x">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="px-3 py-1 hover:bg-gray-100"
                    >
                      +
                    </button>
                  </div>
                  <span className="text-sm text-gray-500 mr-4 arabic">
                    متوفر {book.stockCount} نسخة
                  </span>
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button className="flex-1 arabic" size="lg">
                  أضف إلى السلة
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  onClick={() => setIsWishlisted(!isWishlisted)}
                >
                  {isWishlisted ? (
                    <HeartSolidIcon className="h-5 w-5 text-red-500" />
                  ) : (
                    <HeartIcon className="h-5 w-5" />
                  )}
                </Button>
                <Button variant="outline" size="lg">
                  <ShareIcon className="h-5 w-5" />
                </Button>
              </div>
            </div>

            {/* Features */}
            <div className="space-y-2">
              {book.features.map((feature, index) => (
                <div key={index} className="flex items-center text-sm text-gray-600">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
                  <span className="arabic">{feature}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 space-x-reverse px-6">
              {[
                { id: 'description', name: 'الوصف' },
                { id: 'details', name: 'التفاصيل' },
                { id: 'contents', name: 'المحتويات' },
                { id: 'reviews', name: 'التقييمات' },
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm arabic ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>

          <div className="p-6">
            {activeTab === 'description' && (
              <div className="prose max-w-none arabic">
                <p className="text-gray-700 leading-relaxed">
                  {book.description}
                </p>
              </div>
            )}

            {activeTab === 'details' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold mb-3 arabic">معلومات الكتاب</h4>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">عدد الصفحات:</dt>
                      <dd className="font-medium">{book.pages}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">اللغة:</dt>
                      <dd className="font-medium arabic">{book.language}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">الناشر:</dt>
                      <dd className="font-medium arabic">{book.publisher}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">سنة النشر:</dt>
                      <dd className="font-medium">{book.publishYear}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">ISBN:</dt>
                      <dd className="font-medium">{book.isbn}</dd>
                    </div>
                  </dl>
                </div>
                <div>
                  <h4 className="font-semibold mb-3 arabic">المواصفات الفيزيائية</h4>
                  <dl className="space-y-2">
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">الوزن:</dt>
                      <dd className="font-medium arabic">{book.weight}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-gray-600 arabic">الأبعاد:</dt>
                      <dd className="font-medium arabic">{book.dimensions}</dd>
                    </div>
                  </dl>
                </div>
              </div>
            )}

            {activeTab === 'contents' && (
              <div>
                <h4 className="font-semibold mb-4 arabic">فهرس المحتويات</h4>
                <ol className="space-y-2">
                  {book.tableOfContents.map((chapter, index) => (
                    <li key={index} className="flex items-center arabic">
                      <span className="w-8 h-8 bg-primary/10 text-primary rounded-full flex items-center justify-center text-sm font-medium mr-3">
                        {index + 1}
                      </span>
                      {chapter}
                    </li>
                  ))}
                </ol>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <div className="flex items-center justify-between mb-6">
                  <h4 className="font-semibold arabic">تقييمات العملاء</h4>
                  <Button variant="outline" className="arabic">
                    اكتب تقييم
                  </Button>
                </div>
                <div className="space-y-6">
                  {reviews.map((review) => (
                    <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center">
                          <span className="font-medium arabic">{review.name}</span>
                          <div className="flex items-center mr-3">
                            {[...Array(5)].map((_, i) => (
                              <StarIcon
                                key={i}
                                className={`h-4 w-4 ${
                                  i < review.rating
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                        <span className="text-sm text-gray-500">
                          {new Date(review.date).toLocaleDateString('ar-EG')}
                        </span>
                      </div>
                      <p className="text-gray-700 arabic">{review.comment}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Books */}
        <div className="mt-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 arabic">
            كتب ذات صلة
          </h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {relatedBooks.map((relatedBook) => (
              <Card key={relatedBook.id} className="hover:shadow-lg transition-shadow">
                <div className="bg-gray-200 rounded-t-lg aspect-[3/4] flex items-center justify-center">
                  <span className="text-4xl">📖</span>
                </div>
                <CardContent className="p-4">
                  <h4 className="font-semibold mb-1 arabic line-clamp-2">
                    {relatedBook.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-2 arabic">
                    {relatedBook.author}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(relatedBook.rating)
                              ? 'text-yellow-400 fill-current'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="font-bold text-primary">
                      {relatedBook.price} ج.م
                    </span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
