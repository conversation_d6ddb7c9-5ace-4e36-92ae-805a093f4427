'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { TrashIcon, PlusIcon, MinusIcon, TruckIcon, TagIcon } from '@heroicons/react/24/outline';

export default function CartPage() {
  const [cartItems, setCartItems] = useState([
    {
      id: 1,
      title: 'مئة عام من العزلة',
      author: 'غابرييل غارسيا ماركيز',
      price: 120,
      originalPrice: 150,
      quantity: 2,
      format: 'physical',
      formatName: 'نسخة ورقية',
      inStock: true,
      image: '/books/book1.jpg',
    },
    {
      id: 2,
      title: 'فن اللامبالاة',
      author: 'مارك مانسون',
      price: 60,
      originalPrice: null,
      quantity: 1,
      format: 'pdf',
      formatName: 'نسخة PDF',
      inStock: true,
      image: '/books/book2.jpg',
    },
    {
      id: 3,
      title: 'الأسود يليق بك',
      author: 'أحلام مستغانمي',
      price: 95,
      originalPrice: null,
      quantity: 1,
      format: 'physical',
      formatName: 'نسخة ورقية',
      inStock: true,
      image: '/books/book3.jpg',
    },
  ]);

  const [promoCode, setPromoCode] = useState('');
  const [appliedPromo, setAppliedPromo] = useState(null);

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity < 1) return;
    setCartItems(items =>
      items.map(item =>
        item.id === id ? { ...item, quantity: newQuantity } : item
      )
    );
  };

  const removeItem = (id: number) => {
    setCartItems(items => items.filter(item => item.id !== id));
  };

  const applyPromoCode = () => {
    // Simple promo code logic
    if (promoCode === 'SAVE10') {
      setAppliedPromo({ code: 'SAVE10', discount: 10, type: 'percentage' });
    } else if (promoCode === 'FREESHIP') {
      setAppliedPromo({ code: 'FREESHIP', discount: 25, type: 'shipping' });
    } else {
      alert('كود الخصم غير صحيح');
    }
  };

  const removePromoCode = () => {
    setAppliedPromo(null);
    setPromoCode('');
  };

  // Calculations
  const subtotal = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const physicalItems = cartItems.filter(item => item.format === 'physical');
  const hasPhysicalItems = physicalItems.length > 0;
  const shippingCost = hasPhysicalItems ? (subtotal >= 200 ? 0 : 25) : 0;
  const promoDiscount = appliedPromo 
    ? appliedPromo.type === 'percentage' 
      ? (subtotal * appliedPromo.discount / 100)
      : appliedPromo.type === 'shipping'
      ? Math.min(shippingCost, appliedPromo.discount)
      : 0
    : 0;
  const total = subtotal + shippingCost - promoDiscount;

  if (cartItems.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center py-16">
            <div className="text-8xl mb-6">🛒</div>
            <h2 className="text-3xl font-bold text-gray-900 mb-4 arabic">
              سلة التسوق فارغة
            </h2>
            <p className="text-gray-600 mb-8 arabic">
              لم تقم بإضافة أي كتب إلى سلة التسوق بعد
            </p>
            <Link href="/books">
              <Button size="lg" className="arabic">
                تصفح الكتب
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 arabic">
            سلة التسوق
          </h1>
          <p className="text-gray-600 arabic">
            لديك {cartItems.length} عنصر في سلة التسوق
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2 space-y-4">
            {cartItems.map((item) => (
              <Card key={`${item.id}-${item.format}`} className="p-6">
                <div className="flex items-center space-x-4 space-x-reverse">
                  {/* Book Image */}
                  <div className="w-20 h-28 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0">
                    <span className="text-2xl">📖</span>
                  </div>

                  {/* Book Details */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 arabic line-clamp-2">
                          {item.title}
                        </h3>
                        <p className="text-sm text-gray-600 arabic">
                          {item.author}
                        </p>
                        <Badge variant="outline" className="mt-1 arabic text-xs">
                          {item.formatName}
                        </Badge>
                      </div>
                      <button
                        onClick={() => removeItem(item.id)}
                        className="text-gray-400 hover:text-red-500 transition-colors p-1"
                      >
                        <TrashIcon className="h-5 w-5" />
                      </button>
                    </div>

                    {/* Price and Quantity */}
                    <div className="flex items-center justify-between mt-4">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-lg font-bold text-primary">
                          {item.price} ج.م
                        </span>
                        {item.originalPrice && (
                          <span className="text-sm text-gray-500 line-through">
                            {item.originalPrice} ج.م
                          </span>
                        )}
                      </div>

                      {/* Quantity Controls */}
                      <div className="flex items-center border rounded-md">
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          className="p-2 hover:bg-gray-100 transition-colors"
                        >
                          <MinusIcon className="h-4 w-4" />
                        </button>
                        <span className="px-4 py-2 border-x min-w-[3rem] text-center">
                          {item.quantity}
                        </span>
                        <button
                          onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          className="p-2 hover:bg-gray-100 transition-colors"
                        >
                          <PlusIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>

                    {/* Item Total */}
                    <div className="mt-2 text-right">
                      <span className="text-sm text-gray-600 arabic">المجموع: </span>
                      <span className="font-semibold text-primary">
                        {item.price * item.quantity} ج.م
                      </span>
                    </div>
                  </div>
                </div>
              </Card>
            ))}

            {/* Continue Shopping */}
            <div className="pt-4">
              <Link href="/books">
                <Button variant="outline" className="arabic">
                  ← متابعة التسوق
                </Button>
              </Link>
            </div>
          </div>

          {/* Order Summary */}
          <div className="space-y-6">
            {/* Promo Code */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center arabic">
                  <TagIcon className="h-5 w-5 ml-2" />
                  كود الخصم
                </CardTitle>
              </CardHeader>
              <CardContent>
                {!appliedPromo ? (
                  <div className="space-y-3">
                    <input
                      type="text"
                      placeholder="أدخل كود الخصم"
                      value={promoCode}
                      onChange={(e) => setPromoCode(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary arabic"
                    />
                    <Button 
                      onClick={applyPromoCode}
                      variant="outline" 
                      className="w-full arabic"
                      disabled={!promoCode.trim()}
                    >
                      تطبيق الكود
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md">
                    <div>
                      <span className="text-sm font-medium text-green-800 arabic">
                        {appliedPromo.code}
                      </span>
                      <p className="text-xs text-green-600 arabic">
                        {appliedPromo.type === 'percentage' 
                          ? `خصم ${appliedPromo.discount}%`
                          : 'شحن مجاني'
                        }
                      </p>
                    </div>
                    <button
                      onClick={removePromoCode}
                      className="text-green-600 hover:text-green-800"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Order Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic">ملخص الطلب</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="arabic">المجموع الفرعي:</span>
                  <span className="font-medium">{subtotal} ج.م</span>
                </div>

                {hasPhysicalItems && (
                  <div className="flex justify-between">
                    <span className="arabic">الشحن:</span>
                    <span className="font-medium">
                      {shippingCost === 0 ? (
                        <span className="text-green-600 arabic">مجاني</span>
                      ) : (
                        `${shippingCost} ج.م`
                      )}
                    </span>
                  </div>
                )}

                {promoDiscount > 0 && (
                  <div className="flex justify-between text-green-600">
                    <span className="arabic">الخصم:</span>
                    <span className="font-medium">-{promoDiscount} ج.م</span>
                  </div>
                )}

                <div className="border-t pt-4">
                  <div className="flex justify-between text-lg font-bold">
                    <span className="arabic">المجموع الكلي:</span>
                    <span className="text-primary">{total} ج.م</span>
                  </div>
                </div>

                {/* Shipping Info */}
                {hasPhysicalItems && (
                  <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                    <div className="flex items-center">
                      <TruckIcon className="h-5 w-5 text-blue-600 ml-2" />
                      <div className="text-sm">
                        {subtotal >= 200 ? (
                          <p className="text-blue-800 arabic">
                            🎉 تهانينا! حصلت على شحن مجاني
                          </p>
                        ) : (
                          <p className="text-blue-800 arabic">
                            أضف {200 - subtotal} ج.م أخرى للحصول على شحن مجاني
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Checkout Button */}
                <Button className="w-full arabic" size="lg">
                  متابعة إلى الدفع
                </Button>

                {/* Security Info */}
                <div className="text-center text-xs text-gray-500 arabic">
                  🔒 دفع آمن ومحمي بتشفير SSL
                </div>
              </CardContent>
            </Card>

            {/* Estimated Delivery */}
            {hasPhysicalItems && (
              <Card>
                <CardHeader>
                  <CardTitle className="arabic">معلومات التوصيل</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="arabic">التوصيل المتوقع:</span>
                      <span className="arabic">3-5 أيام عمل</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="arabic">التوصيل السريع:</span>
                      <span className="arabic">1-2 يوم عمل (+15 ج.م)</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
