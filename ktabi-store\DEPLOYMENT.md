# دليل النشر والتشغيل - Ktabi Store Deployment Guide

## 🚀 تشغيل المشروع محلياً

### المتطلبات الأساسية
- Node.js 18.0.0 أو أحدث
- npm 8.0.0 أو أحدث (أو yarn/pnpm)
- Git

### خطوات التشغيل

1. **استنساخ المشروع**
```bash
git clone https://github.com/ktabi-store/ktabi-store.git
cd ktabi-store
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
# أو
pnpm install
```

3. **إنشاء ملف البيئة**
```bash
cp .env.example .env.local
```

4. **تحرير متغيرات البيئة**
```bash
# .env.local
NEXT_PUBLIC_SITE_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
DATABASE_URL=your_database_url
NEXTAUTH_SECRET=your_secret_key
NEXTAUTH_URL=http://localhost:3000
```

5. **تشغيل الخادم المحلي**
```bash
npm run dev
```

6. **فتح المتصفح**
افتح [http://localhost:3000](http://localhost:3000)

## 🏗️ البناء للإنتاج

### بناء المشروع
```bash
npm run build
```

### اختبار البناء محلياً
```bash
npm run start
```

### فحص الأخطاء
```bash
npm run lint
npm run type-check
```

## 🌐 النشر على Vercel (موصى به)

### النشر التلقائي
1. ادفع الكود إلى GitHub
2. اربط المستودع بـ Vercel
3. سيتم النشر تلقائياً

### النشر اليدوي
```bash
npm install -g vercel
vercel login
vercel --prod
```

### متغيرات البيئة في Vercel
```bash
NEXT_PUBLIC_SITE_URL=https://your-domain.com
NEXT_PUBLIC_API_URL=https://your-domain.com/api
DATABASE_URL=your_production_database_url
NEXTAUTH_SECRET=your_production_secret
NEXTAUTH_URL=https://your-domain.com
```

## 🐳 النشر باستخدام Docker

### إنشاء صورة Docker
```bash
# إنشاء Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### بناء وتشغيل الحاوية
```bash
docker build -t ktabi-store .
docker run -p 3000:3000 ktabi-store
```

## ☁️ النشر على خدمات أخرى

### Netlify
```bash
npm run build
# رفع مجلد out/ إلى Netlify
```

### AWS Amplify
```bash
# إعداد amplify.yml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

### DigitalOcean App Platform
```yaml
# .do/app.yaml
name: ktabi-store
services:
- name: web
  source_dir: /
  github:
    repo: your-username/ktabi-store
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
```

## 🔧 إعدادات الإنتاج

### تحسين الأداء
```javascript
// next.config.js
const nextConfig = {
  compress: true,
  poweredByHeader: false,
  generateEtags: false,
  
  images: {
    domains: ['your-cdn-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  headers: async () => [
    {
      source: '/_next/static/(.*)',
      headers: [
        {
          key: 'Cache-Control',
          value: 'public, max-age=31536000, immutable',
        },
      ],
    },
  ],
};
```

### إعداد CDN
```bash
# استخدام Cloudflare أو AWS CloudFront
# لتسريع تحميل الملفات الثابتة
```

## 📊 المراقبة والتحليل

### Google Analytics
```javascript
// في _app.tsx أو layout.tsx
import { GoogleAnalytics } from '@next/third-parties/google'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>{children}</body>
      <GoogleAnalytics gaId="GA_MEASUREMENT_ID" />
    </html>
  )
}
```

### Sentry للأخطاء
```bash
npm install @sentry/nextjs
```

### Vercel Analytics
```bash
npm install @vercel/analytics
```

## 🔒 الأمان

### متغيرات البيئة الآمنة
```bash
# لا تضع أسرار في NEXT_PUBLIC_*
NEXT_PUBLIC_SITE_URL=https://ktabi-store.com
DATABASE_URL=postgresql://... # آمن
API_SECRET_KEY=secret123 # آمن
```

### Headers الأمان
```javascript
// next.config.js
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on'
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  }
];
```

## 🚨 استكشاف الأخطاء

### مشاكل شائعة

1. **خطأ في البناء**
```bash
npm run clean
npm install
npm run build
```

2. **مشاكل الخطوط العربية**
```css
/* تأكد من تحميل الخطوط في globals.css */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
```

3. **مشاكل RTL**
```html
<!-- تأكد من وجود dir="rtl" في html -->
<html lang="ar" dir="rtl">
```

### سجلات الأخطاء
```bash
# عرض سجلات Vercel
vercel logs

# عرض سجلات محلية
npm run dev -- --debug
```

## 📈 تحسين الأداء

### تحليل الحزمة
```bash
npm run analyze
```

### تحسين الصور
```javascript
// استخدام next/image دائماً
import Image from 'next/image'

<Image
  src="/book-cover.jpg"
  alt="غلاف الكتاب"
  width={300}
  height={400}
  priority={true} // للصور المهمة
/>
```

### Lazy Loading
```javascript
// استخدام dynamic import للمكونات الثقيلة
import dynamic from 'next/dynamic'

const HeavyComponent = dynamic(() => import('./HeavyComponent'), {
  loading: () => <p>جاري التحميل...</p>
})
```

## 🔄 التحديثات

### تحديث التبعيات
```bash
npm update
npm audit fix
```

### تحديث Next.js
```bash
npm install next@latest react@latest react-dom@latest
```

---

**ملاحظة**: تأكد من اختبار جميع الميزات بعد كل نشر، خاصة الدفع وسلة التسوق.
