# =============================================================================
# Ktabi Store Environment Variables
# =============================================================================

# -----------------------------------------------------------------------------
# Application Settings
# -----------------------------------------------------------------------------
NEXT_PUBLIC_APP_NAME="كتابي ستور - Ktabi Store"
NEXT_PUBLIC_APP_DESCRIPTION="متجر الكتب الأول في مصر"
NEXT_PUBLIC_SITE_URL="http://localhost:3000"
NEXT_PUBLIC_API_URL="http://localhost:3000/api"

# -----------------------------------------------------------------------------
# Database Configuration
# -----------------------------------------------------------------------------
# PostgreSQL Database URL
DATABASE_URL="postgresql://username:password@localhost:5432/ktabi_store"

# MongoDB Database URL (alternative)
# MONGODB_URI="mongodb://localhost:27017/ktabi_store"

# -----------------------------------------------------------------------------
# Authentication (NextAuth.js)
# -----------------------------------------------------------------------------
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-super-secret-key-here"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Facebook OAuth
FACEBOOK_CLIENT_ID="your-facebook-app-id"
FACEBOOK_CLIENT_SECRET="your-facebook-app-secret"

# -----------------------------------------------------------------------------
# Payment Gateways
# -----------------------------------------------------------------------------
# Stripe
STRIPE_PUBLISHABLE_KEY="pk_test_..."
STRIPE_SECRET_KEY="sk_test_..."
STRIPE_WEBHOOK_SECRET="whsec_..."

# PayPal
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"

# Fawry (Egyptian Payment Gateway)
FAWRY_MERCHANT_CODE="your-fawry-merchant-code"
FAWRY_SECURITY_KEY="your-fawry-security-key"

# -----------------------------------------------------------------------------
# Email Services
# -----------------------------------------------------------------------------
# SMTP Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="your-app-password"

# SendGrid
SENDGRID_API_KEY="SG.your-sendgrid-api-key"

# Mailgun
MAILGUN_API_KEY="your-mailgun-api-key"
MAILGUN_DOMAIN="your-mailgun-domain"

# -----------------------------------------------------------------------------
# File Storage
# -----------------------------------------------------------------------------
# AWS S3
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="ktabi-store-uploads"

# Cloudinary
CLOUDINARY_CLOUD_NAME="your-cloudinary-cloud-name"
CLOUDINARY_API_KEY="your-cloudinary-api-key"
CLOUDINARY_API_SECRET="your-cloudinary-api-secret"

# -----------------------------------------------------------------------------
# Analytics & Monitoring
# -----------------------------------------------------------------------------
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID="G-XXXXXXXXXX"

# Google Tag Manager
NEXT_PUBLIC_GTM_ID="GTM-XXXXXXX"

# Facebook Pixel
NEXT_PUBLIC_FACEBOOK_PIXEL_ID="your-facebook-pixel-id"

# Sentry (Error Monitoring)
SENTRY_DSN="your-sentry-dsn"
SENTRY_ORG="your-sentry-org"
SENTRY_PROJECT="your-sentry-project"

# -----------------------------------------------------------------------------
# External APIs
# -----------------------------------------------------------------------------
# Google Books API
GOOGLE_BOOKS_API_KEY="your-google-books-api-key"

# OpenLibrary API (Free alternative)
# No API key required for basic usage

# Goodreads API (if available)
GOODREADS_API_KEY="your-goodreads-api-key"

# -----------------------------------------------------------------------------
# Search & Recommendations
# -----------------------------------------------------------------------------
# Algolia Search
ALGOLIA_APPLICATION_ID="your-algolia-app-id"
ALGOLIA_ADMIN_API_KEY="your-algolia-admin-key"
NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY="your-algolia-search-key"

# Elasticsearch
ELASTICSEARCH_URL="http://localhost:9200"
ELASTICSEARCH_USERNAME="elastic"
ELASTICSEARCH_PASSWORD="your-elasticsearch-password"

# -----------------------------------------------------------------------------
# Shipping & Logistics
# -----------------------------------------------------------------------------
# Aramex
ARAMEX_USERNAME="your-aramex-username"
ARAMEX_PASSWORD="your-aramex-password"
ARAMEX_ACCOUNT_NUMBER="your-aramex-account"

# Egypt Post
EGYPT_POST_API_KEY="your-egypt-post-api-key"

# -----------------------------------------------------------------------------
# SMS Services
# -----------------------------------------------------------------------------
# Twilio
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Vodafone Egypt SMS
VODAFONE_SMS_USERNAME="your-vodafone-username"
VODAFONE_SMS_PASSWORD="your-vodafone-password"

# -----------------------------------------------------------------------------
# Social Media Integration
# -----------------------------------------------------------------------------
# WhatsApp Business API
WHATSAPP_BUSINESS_PHONE_ID="your-whatsapp-phone-id"
WHATSAPP_ACCESS_TOKEN="your-whatsapp-access-token"

# Telegram Bot
TELEGRAM_BOT_TOKEN="your-telegram-bot-token"
TELEGRAM_CHAT_ID="your-telegram-chat-id"

# -----------------------------------------------------------------------------
# Development & Testing
# -----------------------------------------------------------------------------
# Environment
NODE_ENV="development"

# Debug Mode
DEBUG="true"

# Test Database
TEST_DATABASE_URL="postgresql://username:password@localhost:5432/ktabi_store_test"

# -----------------------------------------------------------------------------
# Security
# -----------------------------------------------------------------------------
# JWT Secret
JWT_SECRET="your-jwt-secret-key"

# Encryption Key
ENCRYPTION_KEY="your-32-character-encryption-key"

# Rate Limiting
RATE_LIMIT_MAX="100"
RATE_LIMIT_WINDOW="900000"

# -----------------------------------------------------------------------------
# Cache & Redis
# -----------------------------------------------------------------------------
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD="your-redis-password"

# -----------------------------------------------------------------------------
# Backup & Maintenance
# -----------------------------------------------------------------------------
# Database Backup
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS="30"

# Maintenance Mode
MAINTENANCE_MODE="false"
MAINTENANCE_MESSAGE="الموقع تحت الصيانة - سنعود قريباً"

# -----------------------------------------------------------------------------
# Feature Flags
# -----------------------------------------------------------------------------
FEATURE_DIGITAL_BOOKS="true"
FEATURE_WISHLIST="true"
FEATURE_REVIEWS="true"
FEATURE_RECOMMENDATIONS="true"
FEATURE_LOYALTY_PROGRAM="false"

# -----------------------------------------------------------------------------
# Localization
# -----------------------------------------------------------------------------
DEFAULT_LOCALE="ar"
SUPPORTED_LOCALES="ar,en"
DEFAULT_CURRENCY="EGP"
DEFAULT_TIMEZONE="Africa/Cairo"

# -----------------------------------------------------------------------------
# Performance
# -----------------------------------------------------------------------------
# CDN
CDN_URL="https://cdn.ktabi-store.com"

# Image Optimization
IMAGE_QUALITY="80"
IMAGE_FORMATS="webp,avif"

# Cache TTL (in seconds)
CACHE_TTL="3600"

# -----------------------------------------------------------------------------
# Legal & Compliance
# -----------------------------------------------------------------------------
# Privacy Policy URL
PRIVACY_POLICY_URL="/privacy"

# Terms of Service URL
TERMS_OF_SERVICE_URL="/terms"

# Cookie Consent
COOKIE_CONSENT_REQUIRED="true"

# -----------------------------------------------------------------------------
# Contact Information
# -----------------------------------------------------------------------------
CONTACT_EMAIL="<EMAIL>"
CONTACT_PHONE="+***********"
CONTACT_ADDRESS="شارع التحرير، وسط البلد، القاهرة، مصر"

# Support Email
SUPPORT_EMAIL="<EMAIL>"

# Business Hours
BUSINESS_HOURS="السبت - الخميس: 9:00 - 21:00"
