'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Input } from '@/components/ui/Input';
import { 
  PhoneIcon, 
  EnvelopeIcon, 
  MapPinIcon, 
  ClockIcon,
  ChatBubbleLeftRightIcon,
  QuestionMarkCircleIcon
} from '@heroicons/react/24/outline';

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
    type: 'general'
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    alert('تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    setFormData({
      name: '',
      email: '',
      phone: '',
      subject: '',
      message: '',
      type: 'general'
    });
    setIsSubmitting(false);
  };

  const contactInfo = [
    {
      icon: <PhoneIcon className="h-6 w-6" />,
      title: 'الهاتف',
      details: ['+20 ************', '+20 ************'],
      description: 'متاح من 9 صباحاً حتى 9 مساءً'
    },
    {
      icon: <EnvelopeIcon className="h-6 w-6" />,
      title: 'البريد الإلكتروني',
      details: ['<EMAIL>', '<EMAIL>'],
      description: 'نرد خلال 24 ساعة'
    },
    {
      icon: <MapPinIcon className="h-6 w-6" />,
      title: 'العنوان',
      details: ['شارع التحرير، وسط البلد', 'القاهرة، مصر'],
      description: 'مفتوح من السبت للخميس'
    },
    {
      icon: <ClockIcon className="h-6 w-6" />,
      title: 'ساعات العمل',
      details: ['السبت - الخميس: 9:00 - 21:00', 'الجمعة: 14:00 - 21:00'],
      description: 'خدمة العملاء متاحة طوال الوقت'
    }
  ];

  const quickActions = [
    {
      icon: <ChatBubbleLeftRightIcon className="h-8 w-8 text-green-600" />,
      title: 'واتساب',
      description: 'تحدث معنا مباشرة',
      action: 'فتح واتساب',
      color: 'bg-green-50 border-green-200 hover:bg-green-100'
    },
    {
      icon: <QuestionMarkCircleIcon className="h-8 w-8 text-blue-600" />,
      title: 'الأسئلة الشائعة',
      description: 'ابحث عن إجابات سريعة',
      action: 'عرض الأسئلة',
      color: 'bg-blue-50 border-blue-200 hover:bg-blue-100'
    },
    {
      icon: <EnvelopeIcon className="h-8 w-8 text-purple-600" />,
      title: 'الدعم الفني',
      description: 'مساعدة تقنية متخصصة',
      action: 'إرسال تذكرة',
      color: 'bg-purple-50 border-purple-200 hover:bg-purple-100'
    }
  ];

  const faqItems = [
    {
      question: 'كم يستغرق توصيل الطلب؟',
      answer: 'عادة ما يستغرق التوصيل من 3-5 أيام عمل داخل القاهرة والجيزة، و5-7 أيام للمحافظات الأخرى.'
    },
    {
      question: 'هل يمكنني إرجاع الكتاب؟',
      answer: 'نعم، يمكنك إرجاع الكتاب خلال 14 يوم من تاريخ الاستلام بشرط أن يكون في حالته الأصلية.'
    },
    {
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نقبل الدفع نقداً عند الاستلام، التحويل البنكي، فوري، وبطاقات الائتمان.'
    },
    {
      question: 'هل تتوفر الكتب بصيغة رقمية؟',
      answer: 'نعم، نوفر العديد من الكتب بصيغة PDF و EPUB للتحميل الفوري.'
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary/10 to-accent/10 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 arabic">
            تواصل معنا
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic leading-relaxed">
            نحن هنا لمساعدتك! تواصل معنا بأي طريقة تناسبك وسنكون سعداء للرد على استفساراتك
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {quickActions.map((action, index) => (
            <Card key={index} className={`cursor-pointer transition-all duration-200 ${action.color}`}>
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  {action.icon}
                </div>
                <h3 className="text-lg font-semibold mb-2 arabic">{action.title}</h3>
                <p className="text-gray-600 text-sm mb-4 arabic">{action.description}</p>
                <Button variant="outline" size="sm" className="arabic">
                  {action.action}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle className="arabic">أرسل لنا رسالة</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="الاسم الكامل"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      className="arabic"
                      placeholder="أدخل اسمك الكامل"
                    />
                    <Input
                      label="البريد الإلكتروني"
                      name="email"
                      type="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="رقم الهاتف"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="arabic"
                      placeholder="01234567890"
                    />
                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2 arabic">
                        نوع الاستفسار
                      </label>
                      <select
                        name="type"
                        value={formData.type}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring arabic"
                      >
                        <option value="general">استفسار عام</option>
                        <option value="order">استفسار عن طلب</option>
                        <option value="technical">مشكلة تقنية</option>
                        <option value="complaint">شكوى</option>
                        <option value="suggestion">اقتراح</option>
                      </select>
                    </div>
                  </div>

                  <Input
                    label="موضوع الرسالة"
                    name="subject"
                    value={formData.subject}
                    onChange={handleInputChange}
                    required
                    className="arabic"
                    placeholder="اكتب موضوع رسالتك"
                  />

                  <div>
                    <label className="block text-sm font-medium text-foreground mb-2 arabic">
                      الرسالة
                    </label>
                    <textarea
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      className="w-full px-3 py-2 border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-ring arabic"
                      placeholder="اكتب رسالتك هنا..."
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full arabic" 
                    size="lg"
                    loading={isSubmitting}
                  >
                    إرسال الرسالة
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Contact Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="arabic">معلومات التواصل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {contactInfo.map((info, index) => (
                  <div key={index} className="flex items-start space-x-4 space-x-reverse">
                    <div className="flex-shrink-0 w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center text-primary">
                      {info.icon}
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900 mb-1 arabic">{info.title}</h3>
                      {info.details.map((detail, idx) => (
                        <p key={idx} className="text-gray-700 arabic">{detail}</p>
                      ))}
                      <p className="text-sm text-gray-500 mt-1 arabic">{info.description}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Map Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle className="arabic">موقعنا</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-gray-200 rounded-lg h-48 flex items-center justify-center">
                  <div className="text-center text-gray-500">
                    <MapPinIcon className="h-12 w-12 mx-auto mb-2" />
                    <p className="arabic">خريطة الموقع</p>
                    <p className="text-sm arabic">شارع التحرير، وسط البلد، القاهرة</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <Card>
            <CardHeader>
              <CardTitle className="arabic">الأسئلة الشائعة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {faqItems.map((item, index) => (
                  <div key={index} className="border-b border-gray-200 pb-4 last:border-b-0">
                    <h3 className="font-semibold text-gray-900 mb-2 arabic">
                      {item.question}
                    </h3>
                    <p className="text-gray-700 arabic">{item.answer}</p>
                  </div>
                ))}
              </div>
              <div className="mt-6 text-center">
                <Button variant="outline" className="arabic">
                  عرض جميع الأسئلة الشائعة
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Social Media */}
        <div className="mt-12 text-center">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 arabic">
            تابعنا على وسائل التواصل الاجتماعي
          </h3>
          <div className="flex justify-center space-x-6 space-x-reverse">
            <a href="#" className="text-4xl hover:scale-110 transition-transform">📘</a>
            <a href="#" className="text-4xl hover:scale-110 transition-transform">📷</a>
            <a href="#" className="text-4xl hover:scale-110 transition-transform">🐦</a>
            <a href="#" className="text-4xl hover:scale-110 transition-transform">📱</a>
            <a href="#" className="text-4xl hover:scale-110 transition-transform">📺</a>
          </div>
        </div>
      </div>
    </div>
  );
}
