import { create } from 'zustand';
import { persist } from 'zustand/middleware';

export interface CartItem {
  id: number;
  title: string;
  author: string;
  price: number;
  originalPrice?: number;
  quantity: number;
  format: 'physical' | 'pdf' | 'epub';
  formatName: string;
  inStock: boolean;
  image?: string;
  maxQuantity?: number;
}

export interface PromoCode {
  code: string;
  discount: number;
  type: 'percentage' | 'fixed' | 'shipping';
  minAmount?: number;
  maxDiscount?: number;
}

interface CartStore {
  items: CartItem[];
  appliedPromo: PromoCode | null;
  
  // Actions
  addItem: (item: Omit<CartItem, 'quantity'>) => void;
  removeItem: (id: number, format: string) => void;
  updateQuantity: (id: number, format: string, quantity: number) => void;
  clearCart: () => void;
  applyPromoCode: (promo: PromoCode) => void;
  removePromoCode: () => void;
  
  // Getters
  getItemCount: () => number;
  getSubtotal: () => number;
  getShippingCost: () => number;
  getPromoDiscount: () => number;
  getTotal: () => number;
  hasPhysicalItems: () => boolean;
}

const useCartStore = create<CartStore>()(
  persist(
    (set, get) => ({
      items: [],
      appliedPromo: null,

      addItem: (newItem) => {
        set((state) => {
          const existingItemIndex = state.items.findIndex(
            item => item.id === newItem.id && item.format === newItem.format
          );

          if (existingItemIndex >= 0) {
            // Item already exists, increase quantity
            const updatedItems = [...state.items];
            const existingItem = updatedItems[existingItemIndex];
            const maxQty = existingItem.maxQuantity || 10;
            
            updatedItems[existingItemIndex] = {
              ...existingItem,
              quantity: Math.min(existingItem.quantity + 1, maxQty)
            };
            
            return { items: updatedItems };
          } else {
            // Add new item
            return {
              items: [...state.items, { ...newItem, quantity: 1 }]
            };
          }
        });
      },

      removeItem: (id, format) => {
        set((state) => ({
          items: state.items.filter(
            item => !(item.id === id && item.format === format)
          )
        }));
      },

      updateQuantity: (id, format, quantity) => {
        if (quantity < 1) return;
        
        set((state) => ({
          items: state.items.map(item =>
            item.id === id && item.format === format
              ? { ...item, quantity: Math.min(quantity, item.maxQuantity || 10) }
              : item
          )
        }));
      },

      clearCart: () => {
        set({ items: [], appliedPromo: null });
      },

      applyPromoCode: (promo) => {
        const subtotal = get().getSubtotal();
        
        // Check if promo code meets minimum amount requirement
        if (promo.minAmount && subtotal < promo.minAmount) {
          throw new Error(`الحد الأدنى للطلب ${promo.minAmount} ج.م`);
        }
        
        set({ appliedPromo: promo });
      },

      removePromoCode: () => {
        set({ appliedPromo: null });
      },

      getItemCount: () => {
        return get().items.reduce((total, item) => total + item.quantity, 0);
      },

      getSubtotal: () => {
        return get().items.reduce((total, item) => total + (item.price * item.quantity), 0);
      },

      getShippingCost: () => {
        const state = get();
        const hasPhysical = state.hasPhysicalItems();
        const subtotal = state.getSubtotal();
        
        if (!hasPhysical) return 0;
        if (subtotal >= 200) return 0; // Free shipping over 200 EGP
        
        return 25; // Standard shipping cost
      },

      getPromoDiscount: () => {
        const state = get();
        const { appliedPromo } = state;
        
        if (!appliedPromo) return 0;
        
        const subtotal = state.getSubtotal();
        const shippingCost = state.getShippingCost();
        
        switch (appliedPromo.type) {
          case 'percentage':
            const percentageDiscount = (subtotal * appliedPromo.discount) / 100;
            return appliedPromo.maxDiscount 
              ? Math.min(percentageDiscount, appliedPromo.maxDiscount)
              : percentageDiscount;
              
          case 'fixed':
            return Math.min(appliedPromo.discount, subtotal);
            
          case 'shipping':
            return Math.min(appliedPromo.discount, shippingCost);
            
          default:
            return 0;
        }
      },

      getTotal: () => {
        const state = get();
        const subtotal = state.getSubtotal();
        const shipping = state.getShippingCost();
        const discount = state.getPromoDiscount();
        
        return Math.max(0, subtotal + shipping - discount);
      },

      hasPhysicalItems: () => {
        return get().items.some(item => item.format === 'physical');
      },
    }),
    {
      name: 'cart-storage',
      partialize: (state) => ({
        items: state.items,
        appliedPromo: state.appliedPromo,
      }),
    }
  )
);

export default useCartStore;
