import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { StarIcon, TruckIcon, ShieldCheckIcon, CreditCardIcon } from '@heroicons/react/24/solid';

export default function HomePage() {
  // Sample data - in a real app, this would come from an API
  const featuredBooks = [
    {
      id: 1,
      title: 'مئة عام من العزلة',
      author: 'غابرييل غارسيا ماركيز',
      price: 120,
      originalPrice: 150,
      image: '/books/book1.jpg',
      rating: 4.8,
      reviews: 245,
      category: 'روايات',
      isNew: false,
      isBestseller: true,
    },
    {
      id: 2,
      title: 'الأسود يليق بك',
      author: 'أحلام مستغانمي',
      price: 95,
      originalPrice: null,
      image: '/books/book2.jpg',
      rating: 4.6,
      reviews: 189,
      category: 'روايات عربية',
      isNew: true,
      isBestseller: false,
    },
    {
      id: 3,
      title: 'فن اللامبالاة',
      author: 'مارك مانسون',
      price: 85,
      originalPrice: 100,
      image: '/books/book3.jpg',
      rating: 4.7,
      reviews: 312,
      category: 'تطوير ذات',
      isNew: false,
      isBestseller: true,
    },
    {
      id: 4,
      title: 'قواعد العشق الأربعون',
      author: 'إليف شافاق',
      price: 110,
      originalPrice: null,
      image: '/books/book4.jpg',
      rating: 4.9,
      reviews: 428,
      category: 'روايات',
      isNew: false,
      isBestseller: true,
    },
  ];

  const categories = [
    { name: 'الروايات', icon: '📚', count: 1250, href: '/books/novels' },
    { name: 'الكتب الدينية', icon: '🕌', count: 890, href: '/books/religious' },
    { name: 'كتب الأطفال', icon: '🧸', count: 650, href: '/books/children' },
    { name: 'التطوير الذاتي', icon: '🎯', count: 420, href: '/books/self-development' },
    { name: 'التاريخ', icon: '🏛️', count: 380, href: '/books/history' },
    { name: 'العلوم', icon: '🔬', count: 290, href: '/books/science' },
  ];

  const features = [
    {
      icon: <TruckIcon className="h-8 w-8 text-primary" />,
      title: 'شحن مجاني',
      description: 'للطلبات أكثر من 200 جنيه',
    },
    {
      icon: <ShieldCheckIcon className="h-8 w-8 text-primary" />,
      title: 'ضمان الجودة',
      description: 'كتب أصلية 100%',
    },
    {
      icon: <CreditCardIcon className="h-8 w-8 text-primary" />,
      title: 'دفع آمن',
      description: 'طرق دفع متعددة وآمنة',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary/10 to-accent/10 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="text-center lg:text-right">
              <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 arabic">
                اكتشف عالم
                <span className="text-primary"> الكتب</span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 arabic leading-relaxed">
                أكبر مجموعة من الكتب العربية والعالمية في مكان واحد. 
                اطلب كتابك المفضل واستمتع بالقراءة
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Button size="lg" className="arabic">
                  تصفح الكتب
                </Button>
                <Button variant="outline" size="lg" className="arabic">
                  العروض الخاصة
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-300">
                <div className="grid grid-cols-2 gap-4">
                  {featuredBooks.slice(0, 4).map((book, index) => (
                    <div key={book.id} className="relative">
                      <div className="bg-gray-200 rounded-lg aspect-[3/4] flex items-center justify-center">
                        <span className="text-4xl">📖</span>
                      </div>
                      {index === 0 && (
                        <Badge className="absolute -top-2 -right-2 arabic">
                          الأكثر مبيعاً
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2 arabic">{feature.title}</h3>
                <p className="text-gray-600 arabic">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4 arabic">
              تصفح حسب الفئة
            </h2>
            <p className="text-gray-600 arabic">
              اختر من بين مجموعة واسعة من الفئات المختلفة
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category) => (
              <Link key={category.name} href={category.href}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer text-center p-6">
                  <div className="text-4xl mb-4">{category.icon}</div>
                  <h3 className="font-semibold mb-2 arabic">{category.name}</h3>
                  <p className="text-sm text-gray-500 arabic">
                    {category.count} كتاب
                  </p>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Books Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center mb-12">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4 arabic">
                الكتب المميزة
              </h2>
              <p className="text-gray-600 arabic">
                اكتشف أحدث الكتب والأكثر مبيعاً
              </p>
            </div>
            <Link href="/books">
              <Button variant="outline" className="arabic">
                عرض الكل
              </Button>
            </Link>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredBooks.map((book) => (
              <Card key={book.id} className="hover:shadow-lg transition-shadow">
                <div className="relative">
                  <div className="bg-gray-200 rounded-t-lg aspect-[3/4] flex items-center justify-center">
                    <span className="text-6xl">📖</span>
                  </div>
                  {book.isNew && (
                    <Badge className="absolute top-2 right-2 arabic">جديد</Badge>
                  )}
                  {book.isBestseller && (
                    <Badge variant="secondary" className="absolute top-2 left-2 arabic">
                      الأكثر مبيعاً
                    </Badge>
                  )}
                </div>
                <CardContent className="p-4">
                  <Badge variant="outline" className="mb-2 arabic text-xs">
                    {book.category}
                  </Badge>
                  <h3 className="font-semibold mb-1 arabic line-clamp-2">
                    {book.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2 arabic">
                    {book.author}
                  </p>
                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(book.rating)
                              ? 'text-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-500 mr-2">
                      ({book.reviews})
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="text-lg font-bold text-primary">
                        {book.price} ج.م
                      </span>
                      {book.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {book.originalPrice} ج.م
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="p-4 pt-0">
                  <Button className="w-full arabic">
                    أضف إلى السلة
                  </Button>
                </CardFooter>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter Section */}
      <section className="py-16 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4 arabic">
            اشترك في النشرة البريدية
          </h2>
          <p className="text-xl mb-8 arabic">
            احصل على آخر الكتب الجديدة والعروض الخاصة
          </p>
          <div className="max-w-md mx-auto flex gap-4">
            <input
              type="email"
              placeholder="أدخل بريدك الإلكتروني"
              className="flex-1 px-4 py-2 rounded-md text-gray-900 arabic"
            />
            <Button variant="secondary" className="arabic">
              اشتراك
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
