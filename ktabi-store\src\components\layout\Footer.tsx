import React from 'react';
import Link from 'next/link';
import { EnvelopeIcon, PhoneIcon, MapPinIcon } from '@heroicons/react/24/outline';

const Footer = () => {
  const quickLinks = [
    { name: 'من نحن', href: '/about' },
    { name: 'اتصل بنا', href: '/contact' },
    { name: 'الأسئلة الشائعة', href: '/faq' },
    { name: 'سياسة الخصوصية', href: '/privacy' },
    { name: 'شروط الاستخدام', href: '/terms' },
  ];

  const categories = [
    { name: 'الروايات', href: '/books/novels' },
    { name: 'الكتب الدينية', href: '/books/religious' },
    { name: 'كتب الأطفال', href: '/books/children' },
    { name: 'الكتب العلمية', href: '/books/science' },
    { name: 'التاريخ', href: '/books/history' },
  ];

  const paymentMethods = [
    { name: 'كاش عند الاستلام', icon: '💵' },
    { name: 'فوري', icon: '📱' },
    { name: 'تحويل بنكي', icon: '🏦' },
    { name: 'بطاقة ائتمان', icon: '💳' },
  ];

  return (
    <footer className="bg-gray-900 text-white">
      {/* Newsletter section */}
      <div className="bg-primary py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h3 className="text-2xl font-bold text-white mb-4 arabic">
              اشترك في النشرة البريدية
            </h3>
            <p className="text-primary-foreground/80 mb-6 arabic">
              احصل على آخر الكتب الجديدة والعروض الخاصة
            </p>
            <div className="max-w-md mx-auto flex gap-4">
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="flex-1 px-4 py-2 rounded-md text-gray-900 arabic"
              />
              <button className="bg-white text-primary px-6 py-2 rounded-md font-medium hover:bg-gray-100 transition-colors arabic">
                اشتراك
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main footer content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company info */}
          <div className="space-y-4">
            <div className="text-2xl font-bold text-primary arabic">
              📚 كتابي ستور
            </div>
            <p className="text-gray-300 arabic leading-relaxed">
              متجرك الأول لبيع الكتب في مصر. نوفر أفضل الكتب بأسعار مناسبة مع خدمة توصيل سريعة وآمنة.
            </p>
            <div className="flex space-x-4 space-x-reverse">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Facebook</span>
                📘
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Instagram</span>
                📷
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">Twitter</span>
                🐦
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <span className="sr-only">WhatsApp</span>
                📱
              </a>
            </div>
          </div>

          {/* Quick links */}
          <div>
            <h3 className="text-lg font-semibold mb-4 arabic">روابط سريعة</h3>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors arabic"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="text-lg font-semibold mb-4 arabic">الأقسام</h3>
            <ul className="space-y-2">
              {categories.map((category) => (
                <li key={category.name}>
                  <Link
                    href={category.href}
                    className="text-gray-300 hover:text-white transition-colors arabic"
                  >
                    {category.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact info */}
          <div>
            <h3 className="text-lg font-semibold mb-4 arabic">تواصل معنا</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 space-x-reverse">
                <PhoneIcon className="h-5 w-5 text-primary" />
                <span className="text-gray-300">01234567890</span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <EnvelopeIcon className="h-5 w-5 text-primary" />
                <span className="text-gray-300"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3 space-x-reverse">
                <MapPinIcon className="h-5 w-5 text-primary" />
                <span className="text-gray-300 arabic">القاهرة، مصر</span>
              </div>
            </div>

            {/* Payment methods */}
            <div className="mt-6">
              <h4 className="text-sm font-semibold mb-3 arabic">طرق الدفع</h4>
              <div className="grid grid-cols-2 gap-2">
                {paymentMethods.map((method) => (
                  <div
                    key={method.name}
                    className="flex items-center space-x-2 space-x-reverse text-sm text-gray-300"
                  >
                    <span>{method.icon}</span>
                    <span className="arabic">{method.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom bar */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm arabic">
              © 2024 كتابي ستور. جميع الحقوق محفوظة.
            </div>
            <div className="flex space-x-6 space-x-reverse mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm arabic">
                سياسة الخصوصية
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm arabic">
                شروط الاستخدام
              </Link>
              <Link href="/shipping" className="text-gray-400 hover:text-white text-sm arabic">
                سياسة الشحن
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
