'use client';

import React, { useState, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardFooter } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Input } from '@/components/ui/Input';
import { StarIcon, FunnelIcon, Squares2X2Icon, ListBulletIcon } from '@heroicons/react/24/outline';

export default function BooksPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [priceRange, setPriceRange] = useState([0, 500]);
  const [sortBy, setSortBy] = useState('popularity');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // Sample books data
  const books = [
    {
      id: 1,
      title: 'مئة عام من العزلة',
      author: 'غابرييل غارسيا ماركيز',
      price: 120,
      originalPrice: 150,
      rating: 4.8,
      reviews: 245,
      category: 'novels',
      categoryName: 'روايات',
      isNew: false,
      isBestseller: true,
      description: 'رواية خيالية للكاتب الكولومبي غابرييل غارسيا ماركيز...',
      pages: 448,
      language: 'عربي',
      publisher: 'دار الآداب',
      publishYear: 2020,
    },
    {
      id: 2,
      title: 'الأسود يليق بك',
      author: 'أحلام مستغانمي',
      price: 95,
      originalPrice: null,
      rating: 4.6,
      reviews: 189,
      category: 'novels',
      categoryName: 'روايات عربية',
      isNew: true,
      isBestseller: false,
      description: 'رواية عربية معاصرة تحكي قصة حب وفقدان...',
      pages: 320,
      language: 'عربي',
      publisher: 'منشورات ضفاف',
      publishYear: 2023,
    },
    {
      id: 3,
      title: 'فن اللامبالاة',
      author: 'مارك مانسون',
      price: 85,
      originalPrice: 100,
      rating: 4.7,
      reviews: 312,
      category: 'self-development',
      categoryName: 'تطوير ذات',
      isNew: false,
      isBestseller: true,
      description: 'كتاب في التطوير الذاتي يعلمك كيف تعيش حياة أفضل...',
      pages: 224,
      language: 'عربي',
      publisher: 'مكتبة جرير',
      publishYear: 2021,
    },
    {
      id: 4,
      title: 'قواعد العشق الأربعون',
      author: 'إليف شافاق',
      price: 110,
      originalPrice: null,
      rating: 4.9,
      reviews: 428,
      category: 'novels',
      categoryName: 'روايات',
      isNew: false,
      isBestseller: true,
      description: 'رواية تاريخية تحكي قصة الشاعر الصوفي جلال الدين الرومي...',
      pages: 384,
      language: 'عربي',
      publisher: 'دار الخيال',
      publishYear: 2019,
    },
    {
      id: 5,
      title: 'البخاري الصحيح',
      author: 'الإمام البخاري',
      price: 200,
      originalPrice: 250,
      rating: 4.9,
      reviews: 156,
      category: 'religious',
      categoryName: 'كتب دينية',
      isNew: false,
      isBestseller: true,
      description: 'أصح كتب الحديث النبوي الشريف...',
      pages: 1200,
      language: 'عربي',
      publisher: 'دار ابن كثير',
      publishYear: 2022,
    },
    {
      id: 6,
      title: 'حكايات الأطفال المصورة',
      author: 'مجموعة مؤلفين',
      price: 45,
      originalPrice: null,
      rating: 4.5,
      reviews: 89,
      category: 'children',
      categoryName: 'كتب أطفال',
      isNew: true,
      isBestseller: false,
      description: 'مجموعة من الحكايات المصورة للأطفال...',
      pages: 64,
      language: 'عربي',
      publisher: 'دار الطفل العربي',
      publishYear: 2024,
    },
  ];

  const categories = [
    { id: 'all', name: 'جميع الفئات' },
    { id: 'novels', name: 'الروايات' },
    { id: 'religious', name: 'الكتب الدينية' },
    { id: 'children', name: 'كتب الأطفال' },
    { id: 'self-development', name: 'التطوير الذاتي' },
    { id: 'history', name: 'التاريخ' },
    { id: 'science', name: 'العلوم' },
  ];

  const sortOptions = [
    { id: 'popularity', name: 'الأكثر شعبية' },
    { id: 'price-low', name: 'السعر: من الأقل للأعلى' },
    { id: 'price-high', name: 'السعر: من الأعلى للأقل' },
    { id: 'rating', name: 'التقييم' },
    { id: 'newest', name: 'الأحدث' },
  ];

  // Filter and sort books
  const filteredBooks = useMemo(() => {
    let filtered = books.filter(book => {
      const matchesSearch = book.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           book.author.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'all' || book.category === selectedCategory;
      const matchesPrice = book.price >= priceRange[0] && book.price <= priceRange[1];
      
      return matchesSearch && matchesCategory && matchesPrice;
    });

    // Sort books
    switch (sortBy) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating);
        break;
      case 'newest':
        filtered.sort((a, b) => b.publishYear - a.publishYear);
        break;
      default:
        filtered.sort((a, b) => b.reviews - a.reviews);
    }

    return filtered;
  }, [books, searchQuery, selectedCategory, priceRange, sortBy]);

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4 arabic">
            تصفح الكتب
          </h1>
          <p className="text-gray-600 arabic">
            اكتشف مجموعتنا الواسعة من الكتب في جميع المجالات
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            {/* Search */}
            <div className="flex-1 w-full lg:w-auto">
              <Input
                type="text"
                placeholder="ابحث عن كتاب أو مؤلف..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="arabic"
              />
            </div>

            {/* Category Filter */}
            <div className="w-full lg:w-auto">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full lg:w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary arabic"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort */}
            <div className="w-full lg:w-auto">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full lg:w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary arabic"
              >
                {sortOptions.map(option => (
                  <option key={option.id} value={option.id}>
                    {option.name}
                  </option>
                ))}
              </select>
            </div>

            {/* View Mode Toggle */}
            <div className="flex gap-2">
              <Button
                variant={viewMode === 'grid' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Squares2X2Icon className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'primary' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <ListBulletIcon className="h-4 w-4" />
              </Button>
            </div>

            {/* Filters Toggle */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className="arabic"
            >
              <FunnelIcon className="h-4 w-4 ml-2" />
              فلاتر
            </Button>
          </div>

          {/* Advanced Filters */}
          {showFilters && (
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Price Range */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 arabic">
                    نطاق السعر
                  </label>
                  <div className="flex items-center gap-2">
                    <input
                      type="number"
                      placeholder="من"
                      value={priceRange[0]}
                      onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                      className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                    <span>-</span>
                    <input
                      type="number"
                      placeholder="إلى"
                      value={priceRange[1]}
                      onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 500])}
                      className="w-20 px-2 py-1 border border-gray-300 rounded text-sm"
                    />
                    <span className="text-sm text-gray-500">ج.م</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Results Count */}
        <div className="flex justify-between items-center mb-6">
          <p className="text-gray-600 arabic">
            عرض {filteredBooks.length} من أصل {books.length} كتاب
          </p>
        </div>

        {/* Books Grid/List */}
        <div className={viewMode === 'grid' 
          ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
          : 'space-y-4'
        }>
          {filteredBooks.map((book) => (
            <Card key={book.id} className={`hover:shadow-lg transition-shadow ${
              viewMode === 'list' ? 'flex flex-row' : ''
            }`}>
              <div className={`relative ${viewMode === 'list' ? 'w-32 flex-shrink-0' : ''}`}>
                <div className={`bg-gray-200 ${
                  viewMode === 'list' 
                    ? 'rounded-l-lg h-full' 
                    : 'rounded-t-lg aspect-[3/4]'
                } flex items-center justify-center`}>
                  <span className="text-4xl">📖</span>
                </div>
                {book.isNew && (
                  <Badge className="absolute top-2 right-2 arabic">جديد</Badge>
                )}
                {book.isBestseller && (
                  <Badge variant="secondary" className="absolute top-2 left-2 arabic">
                    الأكثر مبيعاً
                  </Badge>
                )}
              </div>
              
              <div className={`${viewMode === 'list' ? 'flex-1' : ''}`}>
                <CardContent className="p-4">
                  <Badge variant="outline" className="mb-2 arabic text-xs">
                    {book.categoryName}
                  </Badge>
                  <h3 className="font-semibold mb-1 arabic line-clamp-2">
                    {book.title}
                  </h3>
                  <p className="text-sm text-gray-600 mb-2 arabic">
                    {book.author}
                  </p>
                  
                  {viewMode === 'list' && (
                    <p className="text-sm text-gray-600 mb-2 arabic line-clamp-2">
                      {book.description}
                    </p>
                  )}
                  
                  <div className="flex items-center mb-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <StarIcon
                          key={i}
                          className={`h-4 w-4 ${
                            i < Math.floor(book.rating)
                              ? 'text-yellow-400'
                              : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                    <span className="text-sm text-gray-500 mr-2">
                      ({book.reviews})
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <span className="text-lg font-bold text-primary">
                        {book.price} ج.م
                      </span>
                      {book.originalPrice && (
                        <span className="text-sm text-gray-500 line-through">
                          {book.originalPrice} ج.م
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
                
                <CardFooter className="p-4 pt-0">
                  <div className={`${viewMode === 'list' ? 'flex gap-2' : 'w-full'}`}>
                    <Button className={`arabic ${viewMode === 'list' ? 'flex-1' : 'w-full'}`}>
                      أضف إلى السلة
                    </Button>
                    {viewMode === 'list' && (
                      <Button variant="outline" className="arabic">
                        عرض التفاصيل
                      </Button>
                    )}
                  </div>
                </CardFooter>
              </div>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredBooks.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📚</div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2 arabic">
              لم نجد أي كتب
            </h3>
            <p className="text-gray-600 arabic">
              جرب تغيير معايير البحث أو الفلاتر
            </p>
          </div>
        )}

        {/* Load More */}
        {filteredBooks.length > 0 && (
          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="arabic">
              تحميل المزيد
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
