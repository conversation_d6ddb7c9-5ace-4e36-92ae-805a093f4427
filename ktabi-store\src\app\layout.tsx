import type { Metadata } from "next";
import { Cairo, Inter } from "next/font/google";
import "./globals.css";
import Header from "@/components/layout/Header";
import Footer from "@/components/layout/Footer";

const cairo = Cairo({
  subsets: ["arabic", "latin"],
  variable: "--font-cairo",
  display: "swap",
});

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "كتابي ستور - Ktabi Store | متجر الكتب الأول في مصر",
  description: "متجر كتابي ستور - أفضل متجر لبيع الكتب في مصر. اكتشف مجموعة واسعة من الكتب العربية والإنجليزية مع خدمة توصيل سريعة وآمنة.",
  keywords: "كتب، متجر كتب، كتب عربية، روايات، كتب دينية، كتب أطفال، مصر، توصيل كتب",
  authors: [{ name: "Ktabi Store" }],
  creator: "Ktabi Store",
  publisher: "Ktabi Store",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: "كتابي ستور - Ktabi Store",
    description: "متجر الكتب الأول في مصر - اكتشف مجموعة واسعة من الكتب",
    url: "https://ktabi-store.com",
    siteName: "Ktabi Store",
    locale: "ar_EG",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "كتابي ستور - Ktabi Store",
    description: "متجر الكتب الأول في مصر",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="icon" href="/favicon.ico" />
        <meta name="theme-color" content="#0ea5e9" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body
        className={`${cairo.variable} ${inter.variable} antialiased min-h-screen flex flex-col`}
      >
        <Header />
        <main className="flex-1">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
