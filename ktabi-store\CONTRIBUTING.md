# دليل المساهمة - Contributing Guide

مرحباً بك في مشروع كتابي ستور! نحن نرحب بمساهماتك لتحسين المتجر وإضافة ميزات جديدة.

## 🤝 كيفية المساهمة

### 1. إعداد البيئة المحلية

```bash
# استنساخ المشروع
git clone https://github.com/ktabi-store/ktabi-store.git
cd ktabi-store

# تثبيت التبعيات
npm install

# تشغيل الخادم المحلي
npm run dev
```

### 2. إنشاء فرع جديد

```bash
# إنشاء فرع للميزة الجديدة
git checkout -b feature/اسم-الميزة

# أو لإصلاح خطأ
git checkout -b fix/وصف-الخطأ
```

### 3. تطبيق التغييرات

- اكتب كود نظيف ومفهوم
- اتبع معايير التصميم الموجودة
- أضف تعليقات باللغة العربية للكود العربي
- تأكد من دعم RTL في التصميم

### 4. اختبار التغييرات

```bash
# فحص الأخطاء
npm run lint

# فحص TypeScript
npm run type-check

# بناء المشروع
npm run build
```

### 5. إرسال Pull Request

- اكتب وصف واضح للتغييرات
- أضف لقطات شاشة إذا كانت التغييرات بصرية
- اربط Issue ذات الصلة إن وجدت

## 📝 معايير الكود

### JavaScript/TypeScript

```typescript
// استخدم أسماء متغيرات واضحة
const bookTitle = 'عنوان الكتاب';
const userCart = [];

// استخدم TypeScript interfaces
interface Book {
  id: number;
  title: string;
  author: string;
  price: number;
}

// استخدم async/await بدلاً من .then()
const fetchBooks = async (): Promise<Book[]> => {
  try {
    const response = await fetch('/api/books');
    return await response.json();
  } catch (error) {
    console.error('خطأ في جلب الكتب:', error);
    return [];
  }
};
```

### CSS/Tailwind

```css
/* استخدم فئات Tailwind المناسبة للعربية */
.arabic-text {
  @apply text-right font-arabic;
}

/* استخدم متغيرات CSS للألوان */
.primary-button {
  @apply bg-primary text-primary-foreground hover:bg-primary/90;
}
```

### React Components

```tsx
// استخدم TypeScript مع React
interface BookCardProps {
  book: Book;
  onAddToCart: (book: Book) => void;
}

const BookCard: React.FC<BookCardProps> = ({ book, onAddToCart }) => {
  return (
    <div className="card p-4 arabic">
      <h3 className="text-lg font-semibold">{book.title}</h3>
      <p className="text-gray-600">{book.author}</p>
      <button 
        onClick={() => onAddToCart(book)}
        className="btn btn-primary mt-4"
      >
        أضف إلى السلة
      </button>
    </div>
  );
};
```

## 🎨 معايير التصميم

### الألوان
- **الأساسي**: `#0ea5e9` (أزرق)
- **الذهبي**: `#f59e0b` (ذهبي)
- **البيج**: `#f3e1bc` (بيج فاتح)

### الخطوط
- **العربية**: Cairo, Noto Sans Arabic
- **الإنجليزية**: Inter

### المسافات
- استخدم نظام spacing في Tailwind (4, 8, 12, 16, 24, 32...)
- حافظ على تناسق المسافات في جميع الصفحات

### RTL Support
```tsx
// تأكد من دعم RTL
<div className="flex space-x-4 space-x-reverse">
  <button>زر 1</button>
  <button>زر 2</button>
</div>

// استخدم فئات RTL المناسبة
<div className="text-right arabic">
  النص العربي
</div>
```

## 🐛 الإبلاغ عن الأخطاء

### قبل الإبلاغ
- تأكد من أن الخطأ لم يتم الإبلاغ عنه مسبقاً
- جرب إعادة إنتاج الخطأ في بيئة نظيفة

### معلومات مطلوبة
- وصف واضح للخطأ
- خطوات إعادة الإنتاج
- لقطات شاشة إن أمكن
- معلومات البيئة (متصفح، نظام التشغيل)

### مثال على تقرير خطأ
```markdown
## وصف الخطأ
سلة التسوق لا تحفظ العناصر بعد إعادة تحميل الصفحة

## خطوات الإنتاج
1. أضف كتاب إلى السلة
2. أعد تحميل الصفحة
3. السلة فارغة

## السلوك المتوقع
يجب أن تبقى العناصر في السلة

## البيئة
- المتصفح: Chrome 120
- نظام التشغيل: Windows 11
```

## ✨ اقتراح ميزات جديدة

### قبل الاقتراح
- تأكد من أن الميزة لم تُقترح مسبقاً
- فكر في فائدة الميزة للمستخدمين

### معلومات مطلوبة
- وصف واضح للميزة
- حالات الاستخدام
- مخططات أو رسوم توضيحية إن أمكن

## 🔍 مراجعة الكود

### ما نبحث عنه
- **الوظائف**: هل الكود يعمل كما هو متوقع؟
- **الأداء**: هل الكود محسن للأداء؟
- **الأمان**: هل هناك ثغرات أمنية؟
- **إمكانية القراءة**: هل الكود واضح ومفهوم؟
- **الاختبارات**: هل هناك اختبارات كافية؟

### عملية المراجعة
1. مراجعة تلقائية (CI/CD)
2. مراجعة من قبل أحد المطورين
3. اختبار الميزة
4. دمج الكود

## 📚 الموارد المفيدة

### التوثيق
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [TypeScript Documentation](https://www.typescriptlang.org/docs)

### أدوات التطوير
- [VS Code](https://code.visualstudio.com/) مع إضافات:
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - Arabic Language Pack

### مجتمع المطورين
- [Discord Server](https://discord.gg/ktabi-store)
- [GitHub Discussions](https://github.com/ktabi-store/ktabi-store/discussions)

## 🏆 المساهمون

شكر خاص لجميع المساهمين في المشروع:

<!-- سيتم تحديث هذا القسم تلقائياً -->

## 📄 الترخيص

بمساهمتك في هذا المشروع، فإنك توافق على ترخيص مساهمتك تحت نفس ترخيص المشروع (MIT).

## ❓ أسئلة؟

إذا كان لديك أي أسئلة، لا تتردد في:
- فتح Issue جديد
- التواصل معنا على Discord
- إرسال بريد إلكتروني: <EMAIL>

---

شكراً لك على اهتمامك بالمساهمة في كتابي ستور! 🙏
