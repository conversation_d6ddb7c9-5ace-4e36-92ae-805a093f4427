{"name": "ktabi-store", "version": "1.0.0", "description": "متجر إلكتروني لبيع الكتب في مصر - E-commerce bookstore for Egypt", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rm -rf .next out dist", "analyze": "ANALYZE=true npm run build"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.4", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "clsx": "^2.1.1", "framer-motion": "^11.15.0", "lucide-react": "^0.468.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.4.1", "zustand": "^5.0.2"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.4", "@eslint/eslintrc": "^3", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "@next/bundle-analyzer": "^15.3.4"}, "keywords": ["nextjs", "react", "typescript", "tailwindcss", "ecommerce", "bookstore", "arabic", "rtl", "egypt", "books", "كتب", "متجر إلكتروني"], "author": {"name": "Ktabi Store Team", "email": "<EMAIL>", "url": "https://ktabi-store.com"}, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/ktabi-store/ktabi-store.git"}, "bugs": {"url": "https://github.com/ktabi-store/ktabi-store/issues"}, "homepage": "https://ktabi-store.com", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}