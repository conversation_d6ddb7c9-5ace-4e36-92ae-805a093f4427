# كتابي ستور - Ktabi Store

<div align="center">

![Ktabi Store Logo](public/logo.png)

**متجر إلكتروني متكامل لبيع الكتب في مصر**

[![Next.js](https://img.shields.io/badge/Next.js-15.3.4-black)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-4.0-38B2AC)](https://tailwindcss.com/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

[العرض المباشر](https://ktabi-store.vercel.app) • [التوثيق](./docs) • [المساهمة](./CONTRIBUTING.md) • [النشر](./DEPLOYMENT.md)

</div>

## 📚 نظرة عامة

كتابي ستور هو متجر إلكتروني حديث ومتكامل لبيع الكتب، مصمم خصيصاً للسوق المصري والعربي. يوفر تجربة تسوق سلسة مع دعم كامل للغة العربية ونظام RTL.

### ✨ المميزات الرئيسية

🛍️ **تجربة تسوق متكاملة**
- صفحة رئيسية جذابة مع عرض الكتب المميزة
- نظام بحث متقدم مع فلاتر ذكية
- صفحات تفاصيل شاملة للكتب
- سلة تسوق ذكية مع حفظ العناصر
- نظام كوبونات خصم متطور

🎨 **تصميم عصري ومتجاوب**
- دعم كامل للغة العربية مع خطوط Cairo
- تخطيط RTL مناسب للقراءة العربية
- تصميم متجاوب يعمل على جميع الأجهزة
- ألوان مستوحاة من الثقافة المصرية
- رسوم متحركة سلسة

🔧 **تقنيات حديثة**
- Next.js 14 مع App Router
- TypeScript للأمان والموثوقية
- Tailwind CSS للتصميم السريع
- Zustand لإدارة الحالة
- React Hook Form للنماذج

## 🚀 البدء السريع

### المتطلبات
- Node.js 18.0.0+
- npm 8.0.0+ (أو yarn/pnpm)

### التثبيت

```bash
# استنساخ المشروع
git clone https://github.com/ktabi-store/ktabi-store.git
cd ktabi-store

# تثبيت التبعيات
npm install

# تشغيل الخادم المحلي
npm run dev
```

افتح [http://localhost:3000](http://localhost:3000) لرؤية المتجر.

## 📱 صفحات المتجر

| الصفحة | الوصف | الحالة |
|---------|--------|--------|
| [الرئيسية](/) | عرض الكتب المميزة والفئات | ✅ مكتملة |
| [الكتب](/books) | تصفح وبحث مع فلاتر متقدمة | ✅ مكتملة |
| [تفاصيل الكتاب](/books/[id]) | معلومات شاملة ومراجعات | ✅ مكتملة |
| [سلة التسوق](/cart) | إدارة المشتريات والخصومات | ✅ مكتملة |
| [من نحن](/about) | قصة المتجر وفريق العمل | ✅ مكتملة |
| [اتصل بنا](/contact) | نموذج تواصل ومعلومات | ✅ مكتملة |

## 🛠️ التقنيات المستخدمة

### Frontend
- **Next.js 15.3.4** - إطار React للإنتاج
- **TypeScript** - لغة برمجة مع أنواع ثابتة
- **Tailwind CSS** - إطار CSS للتصميم السريع
- **Heroicons** - مكتبة أيقونات SVG
- **Framer Motion** - مكتبة الرسوم المتحركة

### State Management
- **Zustand** - إدارة حالة بسيطة وقوية
- **React Hook Form** - إدارة النماذج
- **React Hot Toast** - إشعارات المستخدم

### Development Tools
- **ESLint** - فحص جودة الكود
- **Prettier** - تنسيق الكود
- **TypeScript** - فحص الأنواع

## 📁 هيكل المشروع

```
ktabi-store/
├── src/
│   ├── app/                    # صفحات التطبيق (App Router)
│   │   ├── about/             # صفحة من نحن
│   │   ├── books/             # صفحات الكتب
│   │   │   ├── [id]/         # صفحة تفاصيل الكتاب
│   │   │   └── page.tsx      # صفحة قائمة الكتب
│   │   ├── cart/              # سلة التسوق
│   │   ├── contact/           # صفحة اتصل بنا
│   │   ├── globals.css        # الأنماط العامة
│   │   ├── layout.tsx         # التخطيط الرئيسي
│   │   └── page.tsx           # الصفحة الرئيسية
│   ├── components/            # المكونات القابلة لإعادة الاستخدام
│   │   ├── ui/               # مكونات واجهة المستخدم الأساسية
│   │   │   ├── Button.tsx    # مكون الأزرار
│   │   │   ├── Card.tsx      # مكون البطاقات
│   │   │   ├── Input.tsx     # مكون حقول الإدخال
│   │   │   └── Badge.tsx     # مكون الشارات
│   │   └── layout/           # مكونات التخطيط
│   │       ├── Header.tsx    # رأس الصفحة
│   │       └── Footer.tsx    # تذييل الصفحة
│   └── store/                # إدارة الحالة (Zustand)
│       └── cartStore.ts      # متجر سلة التسوق
├── public/                   # الملفات الثابتة
│   ├── icons/               # أيقونات التطبيق
│   ├── manifest.json        # ملف التطبيق التقدمي
│   ├── robots.txt           # إرشادات محركات البحث
│   └── sitemap.xml          # خريطة الموقع
├── docs/                    # التوثيق
├── tailwind.config.ts       # إعدادات Tailwind CSS
├── next.config.ts           # إعدادات Next.js
├── package.json             # تبعيات المشروع
└── README.md               # هذا الملف
```

## 🎨 نظام التصميم

### الألوان
```css
:root {
  --primary: #0ea5e9;      /* أزرق - الثقة والمعرفة */
  --accent: #f59e0b;       /* ذهبي - الجودة والتميز */
  --beige: #f3e1bc;        /* بيج - الدفء والراحة */
}
```

### الخطوط
- **العربية**: Cairo, Noto Sans Arabic
- **الإنجليزية**: Inter

### المكونات
- **Button**: أزرار بأحجام وأنواع مختلفة
- **Card**: بطاقات لعرض المحتوى
- **Input**: حقول إدخال مع دعم العربية
- **Badge**: شارات للتصنيفات والحالات

## 🛒 ميزات سلة التسوق

- ✅ إضافة/حذف العناصر
- ✅ تحديث الكميات
- ✅ حفظ السلة محلياً
- ✅ حساب الشحن التلقائي
- ✅ نظام كوبونات الخصم
- ✅ دعم أنواع الكتب المختلفة (ورقي، PDF، EPUB)

## 📱 الاستجابة والأجهزة

المتجر محسن للعمل على:
- 📱 **الهواتف الذكية** (320px+)
- 📱 **الأجهزة اللوحية** (768px+)
- 💻 **أجهزة الكمبيوتر** (1024px+)
- 🖥️ **الشاشات الكبيرة** (1440px+)

## 🌐 الدعم متعدد اللغات

- 🇸🇦 **العربية** (افتراضي) - دعم كامل لـ RTL
- 🇺🇸 **الإنجليزية** - للمحتوى الدولي

## 🚀 النشر

### Vercel (موصى به)
```bash
npm run build
vercel --prod
```

### Docker
```bash
docker build -t ktabi-store .
docker run -p 3000:3000 ktabi-store
```

للمزيد من خيارات النشر، راجع [دليل النشر](./DEPLOYMENT.md).

## 🧪 الاختبار

```bash
# فحص الأخطاء
npm run lint

# فحص TypeScript
npm run type-check

# بناء المشروع
npm run build

# تشغيل الاختبارات (قريباً)
npm test
```

## 🤝 المساهمة

نرحب بمساهماتك! يرجى قراءة [دليل المساهمة](./CONTRIBUTING.md) للتفاصيل.

### خطوات سريعة:
1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📊 الإحصائيات

![GitHub stars](https://img.shields.io/github/stars/ktabi-store/ktabi-store?style=social)
![GitHub forks](https://img.shields.io/github/forks/ktabi-store/ktabi-store?style=social)
![GitHub issues](https://img.shields.io/github/issues/ktabi-store/ktabi-store)
![GitHub pull requests](https://img.shields.io/github/issues-pr/ktabi-store/ktabi-store)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 التواصل

- 📧 **البريد الإلكتروني**: <EMAIL>
- 📱 **الهاتف**: +20 123 456 7890
- 🌐 **الموقع**: [ktabi-store.com](https://ktabi-store.com)
- 💬 **Discord**: [انضم لمجتمعنا](https://discord.gg/ktabi-store)

## 🙏 شكر وتقدير

شكر خاص لـ:
- [Next.js](https://nextjs.org/) لإطار العمل الرائع
- [Tailwind CSS](https://tailwindcss.com/) للتصميم السهل
- [Heroicons](https://heroicons.com/) للأيقونات الجميلة
- جميع المساهمين في المشروع

---

<div align="center">

**كتابي ستور** - حيث تلتقي المعرفة بالتكنولوجيا 📚✨

صنع بـ ❤️ في مصر

</div>
