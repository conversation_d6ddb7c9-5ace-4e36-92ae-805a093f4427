import React from 'react';
import { Card, CardContent } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { 
  BookOpenIcon, 
  HeartIcon, 
  TruckIcon, 
  ShieldCheckIcon,
  UserGroupIcon,
  GlobeAltIcon,
  AcademicCapIcon,
  SparklesIcon
} from '@heroicons/react/24/outline';

export default function AboutPage() {
  const stats = [
    { number: '10,000+', label: 'كتاب متاح', icon: BookOpenIcon },
    { number: '50,000+', label: 'عميل سعيد', icon: UserGroupIcon },
    { number: '25+', label: 'مدينة نخدمها', icon: GlobeAltIcon },
    { number: '5', label: 'سنوات خبرة', icon: AcademicCapIcon },
  ];

  const values = [
    {
      icon: <BookOpenIcon className="h-8 w-8 text-primary" />,
      title: 'شغف بالكتب',
      description: 'نؤمن بقوة الكتب في تغيير الحياة وإثراء المعرفة',
    },
    {
      icon: <HeartIcon className="h-8 w-8 text-primary" />,
      title: 'خدمة العملاء',
      description: 'رضا عملائنا هو أولويتنا الأولى في كل ما نقوم به',
    },
    {
      icon: <TruckIcon className="h-8 w-8 text-primary" />,
      title: 'توصيل سريع',
      description: 'نضمن وصول كتبك في أسرع وقت ممكن وبأفضل حالة',
    },
    {
      icon: <ShieldCheckIcon className="h-8 w-8 text-primary" />,
      title: 'جودة مضمونة',
      description: 'جميع كتبنا أصلية ومن مصادر موثوقة',
    },
  ];

  const team = [
    {
      name: 'أحمد محمد',
      role: 'المؤسس والمدير التنفيذي',
      description: 'خبرة 15 عام في مجال النشر وتجارة الكتب',
      image: '/team/ahmed.jpg',
    },
    {
      name: 'فاطمة علي',
      role: 'مديرة المحتوى والمشتريات',
      description: 'متخصصة في اختيار أفضل الكتب للقراء العرب',
      image: '/team/fatima.jpg',
    },
    {
      name: 'محمود حسن',
      role: 'مدير التكنولوجيا',
      description: 'مطور خبير يضمن أفضل تجربة تسوق إلكتروني',
      image: '/team/mahmoud.jpg',
    },
    {
      name: 'نورا أحمد',
      role: 'مديرة خدمة العملاء',
      description: 'تضمن حصول كل عميل على أفضل خدمة ممكنة',
      image: '/team/nora.jpg',
    },
  ];

  const milestones = [
    {
      year: '2019',
      title: 'البداية',
      description: 'تأسيس كتابي ستور كمتجر صغير في القاهرة',
    },
    {
      year: '2020',
      title: 'التوسع الرقمي',
      description: 'إطلاق المتجر الإلكتروني وخدمة التوصيل',
    },
    {
      year: '2021',
      title: 'نمو المجموعة',
      description: 'إضافة 5000 عنوان جديد وتوسيع نطاق التوصيل',
    },
    {
      year: '2022',
      title: 'الكتب الرقمية',
      description: 'إطلاق خدمة الكتب الرقمية PDF و EPUB',
    },
    {
      year: '2023',
      title: 'التوسع الجغرافي',
      description: 'الوصول إلى 25 مدينة في جميع أنحاء مصر',
    },
    {
      year: '2024',
      title: 'الذكاء الاصطناعي',
      description: 'إضافة نظام التوصيات الذكية للكتب',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary/10 to-accent/10 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6 arabic">
            من نحن
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto arabic leading-relaxed">
            نحن فريق من عشاق الكتب والقراءة، نسعى لجعل المعرفة في متناول الجميع 
            من خلال توفير أفضل الكتب بأسعار مناسبة وخدمة متميزة
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <stat.icon className="h-12 w-12 text-primary" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-600 arabic">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6 arabic">
                قصتنا
              </h2>
              <div className="space-y-4 text-gray-700 arabic leading-relaxed">
                <p>
                  بدأت رحلة "كتابي ستور" في عام 2019 من حلم بسيط: جعل الكتب الجيدة 
                  في متناول كل قارئ عربي. انطلقنا من متجر صغير في قلب القاهرة، 
                  ومع الوقت نمونا لنصبح واحداً من أكبر متاجر الكتب الإلكترونية في مصر.
                </p>
                <p>
                  نؤمن بأن القراءة ليست مجرد هواية، بل هي رحلة اكتشاف وتعلم مستمر. 
                  لذلك نحرص على اختيار كل كتاب في مجموعتنا بعناية فائقة، 
                  لنضمن تقديم محتوى عالي الجودة يلبي احتياجات قرائنا المتنوعة.
                </p>
                <p>
                  اليوم، نفخر بخدمة أكثر من 50,000 عميل سعيد، وتوفير أكثر من 
                  10,000 عنوان في مختلف المجالات، مع خدمة توصيل تغطي 25 مدينة 
                  في جميع أنحاء مصر.
                </p>
              </div>
            </div>
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-xl p-8">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-primary/10 rounded-lg p-6 text-center">
                    <SparklesIcon className="h-8 w-8 text-primary mx-auto mb-2" />
                    <div className="text-2xl font-bold text-primary">2019</div>
                    <div className="text-sm text-gray-600 arabic">سنة التأسيس</div>
                  </div>
                  <div className="bg-accent/10 rounded-lg p-6 text-center">
                    <BookOpenIcon className="h-8 w-8 text-accent mx-auto mb-2" />
                    <div className="text-2xl font-bold text-accent">10K+</div>
                    <div className="text-sm text-gray-600 arabic">كتاب متاح</div>
                  </div>
                  <div className="bg-green-100 rounded-lg p-6 text-center">
                    <UserGroupIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-green-600">50K+</div>
                    <div className="text-sm text-gray-600 arabic">عميل سعيد</div>
                  </div>
                  <div className="bg-blue-100 rounded-lg p-6 text-center">
                    <GlobeAltIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-blue-600">25</div>
                    <div className="text-sm text-gray-600 arabic">مدينة</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4 arabic">
              قيمنا ومبادئنا
            </h2>
            <p className="text-gray-600 arabic">
              المبادئ التي نؤمن بها وتوجه عملنا كل يوم
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <div className="flex justify-center mb-4">
                  {value.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 arabic">{value.title}</h3>
                <p className="text-gray-600 arabic">{value.description}</p>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Timeline Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4 arabic">
              رحلتنا عبر السنين
            </h2>
            <p className="text-gray-600 arabic">
              المحطات المهمة في تطور كتابي ستور
            </p>
          </div>
          <div className="relative">
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary/20"></div>
            <div className="space-y-12">
              {milestones.map((milestone, index) => (
                <div key={index} className={`flex items-center ${
                  index % 2 === 0 ? 'flex-row-reverse' : ''
                }`}>
                  <div className={`w-1/2 ${index % 2 === 0 ? 'pl-8' : 'pr-8'}`}>
                    <Card className="p-6">
                      <div className="flex items-center mb-3">
                        <Badge variant="outline" className="mr-3">
                          {milestone.year}
                        </Badge>
                        <h3 className="text-xl font-semibold arabic">{milestone.title}</h3>
                      </div>
                      <p className="text-gray-600 arabic">{milestone.description}</p>
                    </Card>
                  </div>
                  <div className="w-4 h-4 bg-primary rounded-full border-4 border-white shadow-lg z-10"></div>
                  <div className="w-1/2"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4 arabic">
              فريق العمل
            </h2>
            <p className="text-gray-600 arabic">
              الأشخاص المبدعون وراء نجاح كتابي ستور
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <Card key={index} className="text-center overflow-hidden hover:shadow-lg transition-shadow">
                <div className="bg-gray-200 h-48 flex items-center justify-center">
                  <span className="text-4xl">👤</span>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-2 arabic">{member.name}</h3>
                  <Badge variant="outline" className="mb-3 arabic">
                    {member.role}
                  </Badge>
                  <p className="text-gray-600 text-sm arabic">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 bg-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-6 arabic">
            رسالتنا ورؤيتنا
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mt-12">
            <div>
              <h3 className="text-2xl font-semibold mb-4 arabic">رسالتنا</h3>
              <p className="text-primary-foreground/90 arabic leading-relaxed">
                نسعى لنشر المعرفة والثقافة في العالم العربي من خلال توفير أفضل الكتب 
                بأسعار مناسبة وخدمة متميزة، ونؤمن بأن القراءة هي مفتاح التقدم والنمو الشخصي.
              </p>
            </div>
            <div>
              <h3 className="text-2xl font-semibold mb-4 arabic">رؤيتنا</h3>
              <p className="text-primary-foreground/90 arabic leading-relaxed">
                أن نكون المنصة الأولى للكتب في العالم العربي، ونساهم في بناء مجتمع 
                قارئ ومثقف يقدر المعرفة ويسعى للتعلم المستمر.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
